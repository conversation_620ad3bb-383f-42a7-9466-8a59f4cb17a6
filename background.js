// Background script for ClipHistory Chrome Extension

// Check if a tab can be scripted
function canScriptTab(tab) {
  if (!tab || !tab.url) return false;

  // Can't script chrome:// pages, extension pages, or other restricted URLs
  const restrictedProtocols = ['chrome:', 'chrome-extension:', 'moz-extension:', 'edge:', 'about:'];
  return !restrictedProtocols.some(protocol => tab.url.startsWith(protocol));
}

// Handle action button clicks
chrome.action.onClicked.addListener(async (tab) => {
  console.log('Action button clicked, tab:', tab.url);

  if (!canScriptTab(tab)) {
    console.warn('Cannot script this tab:', tab.url);
    return;
  }

  try {
    // First, try to send a message to existing content script
    await chrome.tabs.sendMessage(tab.id, { action: 'toggle' });
    console.log('Message sent to content script successfully');
  } catch (messageError) {
    console.log('Content script not ready, injecting toggle function:', messageError.message);

    try {
      // If message fails, inject the toggle function directly
      await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: toggleClipboardWidget
      });
      console.log('Toggle function injected successfully');
    } catch (scriptError) {
      console.error('Failed to inject toggle function:', scriptError);
    }
  }
});

// Handle keyboard commands
chrome.commands.onCommand.addListener(async (command) => {
  if (command === 'toggle-clipboard') {
    console.log('Keyboard command triggered:', command);

    try {
      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tab || !tab.id) {
        console.error('No active tab found');
        return;
      }

      if (!canScriptTab(tab)) {
        console.warn('Cannot script this tab:', tab.url);
        return;
      }

      try {
        // First, try to send a message to existing content script
        await chrome.tabs.sendMessage(tab.id, { action: 'toggle' });
        console.log('Command message sent to content script successfully');
      } catch (messageError) {
        console.log('Content script not ready, injecting toggle function:', messageError.message);

        try {
          // If message fails, inject the toggle function directly
          await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: toggleClipboardWidget
          });
          console.log('Command toggle function injected successfully');
        } catch (scriptError) {
          console.error('Failed to inject toggle function via command:', scriptError);
        }
      }
    } catch (error) {
      console.error('Failed to handle keyboard command:', error);
    }
  }
});

// Function to be injected into the page to toggle the widget
function toggleClipboardWidget() {
  // Dispatch a custom event that the content script can listen for
  const event = new CustomEvent('cliphistory-toggle', { 
    detail: { action: 'toggle' } 
  });
  document.dispatchEvent(event);
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('ClipHistory extension installed');
  } else if (details.reason === 'update') {
    console.log('ClipHistory extension updated');
  }
});
