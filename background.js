// Background script for ClipHistory Chrome Extension

// Handle action button clicks
chrome.action.onClicked.addListener(async (tab) => {
  try {
    // Inject and execute the toggle function in the active tab
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: toggleClipboardWidget
    });
  } catch (error) {
    console.error('Failed to toggle clipboard widget:', error);
  }
});

// Handle keyboard commands
chrome.commands.onCommand.addListener(async (command) => {
  if (command === 'toggle-clipboard') {
    try {
      // Get the active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (tab && tab.id) {
        // Inject and execute the toggle function in the active tab
        await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: toggleClipboardWidget
        });
      }
    } catch (error) {
      console.error('Failed to toggle clipboard widget via command:', error);
    }
  }
});

// Function to be injected into the page to toggle the widget
function toggleClipboardWidget() {
  // Dispatch a custom event that the content script can listen for
  const event = new CustomEvent('cliphistory-toggle', { 
    detail: { action: 'toggle' } 
  });
  document.dispatchEvent(event);
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('ClipHistory extension installed');
  } else if (details.reason === 'update') {
    console.log('ClipHistory extension updated');
  }
});
