{"$schema": "https://json.schemastore.org/chrome-manifest", "manifest_version": 3, "name": "ClipHistory - Clipboard Manager", "version": "1.0.1", "description": "A clipboard history application that allows users to view and manage their clipboard history with keyboard shortcuts.", "permissions": ["storage", "clipboardRead", "clipboardWrite", "scripting", "activeTab"], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "background": {"service_worker": "background.js"}, "action": {"default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "default_title": "ClipHistory (Ctrl+Shift+C)"}, "commands": {"toggle-clipboard": {"suggested_key": {"default": "Ctrl+Shift+C"}, "description": "Toggle clipboard history widget"}}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end"}]}