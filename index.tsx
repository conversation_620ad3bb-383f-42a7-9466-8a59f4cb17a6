import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';

// This content script is injected into every page.
// It will create a root element and render the React app into it.

// Check if we're in a Chrome extension context
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
  // Prevent multiple instances
  if (!document.getElementById('cliphistory-react-root')) {
    // 1. Create a container for the app to live in
    const appContainer = document.createElement('div');
    appContainer.id = 'cliphistory-react-root';
    appContainer.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      pointer-events: none !important;
      z-index: 2147483647 !important;
      font-family: system-ui, -apple-system, sans-serif !important;
    `;
    document.documentElement.appendChild(appContainer);

    // 2. Inject Tailwind CSS for styling
    const tailwindScript = document.createElement('script');
    tailwindScript.src = 'https://cdn.tailwindcss.com';
    tailwindScript.onload = () => {
      // Configure Tailwind to work in our isolated context
      if (window.tailwind) {
        window.tailwind.config = {
          prefix: 'tw-',
          important: '#cliphistory-react-root'
        };
      }
    };
    document.head.appendChild(tailwindScript);

    // 3. Inject custom global styles, scoped to the app's container
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      /* Reset styles for the widget container */
      #cliphistory-react-root * {
        box-sizing: border-box !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* Custom scrollbar for the widget */
      #cliphistory-react-root ::-webkit-scrollbar {
        width: 8px !important;
      }
      #cliphistory-react-root ::-webkit-scrollbar-track {
        background: #2d3748 !important; /* gray-800 */
      }
      #cliphistory-react-root ::-webkit-scrollbar-thumb {
        background: #4a5568 !important; /* gray-600 */
        border-radius: 4px !important;
      }
      #cliphistory-react-root ::-webkit-scrollbar-thumb:hover {
        background: #718096 !important; /* gray-500 */
      }

      /* Ensure our modal overlay works properly */
      #cliphistory-react-root > div {
        pointer-events: auto !important;
      }
    `;
    document.head.appendChild(styleElement);

    // 4. Render the React App into the container
    const root = ReactDOM.createRoot(appContainer);
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
  }
} else {
  console.warn('ClipHistory: Not running in Chrome extension context');
}
